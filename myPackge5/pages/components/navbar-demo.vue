<template>
  <view class="page">
    <!-- 自定义导航栏 -->
    <custom-navbar 
      :title="pageTitle" 
      :show-back="true"
      :back-text="backText"
      :bg-color="navBgColor"
      :title-color="navTitleColor"
      :back-color="navBackColor"
      @back="handleBack"
    />
    
    <!-- 页面内容 -->
    <view class="content" :style="contentStyle">
      <view class="demo-section">
        <text class="section-title">导航栏配置</text>
        
        <view class="config-item">
          <text class="config-label">标题：</text>
          <input 
            class="config-input" 
            v-model="pageTitle" 
            placeholder="请输入标题"
          />
        </view>
        
        <view class="config-item">
          <text class="config-label">返回文字：</text>
          <input 
            class="config-input" 
            v-model="backText" 
            placeholder="返回按钮文字（可选）"
          />
        </view>
        
        <view class="config-item">
          <text class="config-label">背景颜色：</text>
          <picker mode="selector" :range="bgColors" @change="changeBgColor">
            <view class="picker-view">
              <text>{{ bgColors[bgColorIndex] }}</text>
              <text class="picker-arrow">></text>
            </view>
          </picker>
        </view>
        
        <view class="config-item">
          <text class="config-label">标题颜色：</text>
          <picker mode="selector" :range="textColors" @change="changeTitleColor">
            <view class="picker-view">
              <text>{{ textColors[titleColorIndex] }}</text>
              <text class="picker-arrow">></text>
            </view>
          </picker>
        </view>
      </view>
      
      <view class="demo-section">
        <text class="section-title">使用说明</text>
        <view class="usage-text">
          <text>1. 该组件适用于设置了 "navigationStyle": "custom" 的页面</text>
          <text>2. 自动适配不同设备的状态栏高度和胶囊按钮位置</text>
          <text>3. 支持自定义标题、颜色、返回按钮等</text>
          <text>4. 右侧自动为小程序胶囊按钮预留空间</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from './custom-navbar.vue'
import { getSystemInfo } from '@/utils/getLocation'

export default {
  components: {
    CustomNavbar
  },
  data() {
    return {
      pageTitle: '活管管理',
      backText: '',
      bgColors: ['白色', '蓝色', '绿色', '红色'],
      bgColorValues: ['#ffffff', '#007AFF', '#10C878', '#FF3B30'],
      bgColorIndex: 0,
      textColors: ['黑色', '白色', '蓝色'],
      textColorValues: ['#333333', '#ffffff', '#007AFF'],
      titleColorIndex: 0,
      systemInfo: {}
    }
  },
  computed: {
    navBgColor() {
      return this.bgColorValues[this.bgColorIndex]
    },
    navTitleColor() {
      return this.textColorValues[this.titleColorIndex]
    },
    navBackColor() {
      return this.textColorValues[this.titleColorIndex]
    },
    contentStyle() {
      // 为固定定位的导航栏预留空间
      const paddingTop = this.systemInfo.customBar || 88
      return {
        paddingTop: paddingTop + 'px'
      }
    }
  },
  created() {
    this.systemInfo = getSystemInfo()
  },
  methods: {
    changeBgColor(e) {
      this.bgColorIndex = e.detail.value
    },
    changeTitleColor(e) {
      this.titleColorIndex = e.detail.value
    },
    handleBack() {
      console.log('自定义返回事件')
      // 可以在这里处理自定义的返回逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 20px;
}

.demo-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 16px;
    display: block;
  }
}

.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  
  .config-label {
    width: 80px;
    font-size: 14px;
    color: #666666;
  }
  
  .config-input {
    flex: 1;
    height: 40px;
    padding: 0 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
  }
  
  .picker-view {
    flex: 1;
    height: 40px;
    padding: 0 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    
    .picker-arrow {
      color: #999999;
    }
  }
}

.usage-text {
  text {
    display: block;
    font-size: 14px;
    color: #666666;
    line-height: 1.6;
    margin-bottom: 8px;
  }
}
</style>
