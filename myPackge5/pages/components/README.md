# 自定义小程序导航栏组件

## 组件说明

`custom-navbar.vue` 是一个专为小程序设计的自定义导航栏组件，适用于设置了 `"navigationStyle": "custom"` 的页面。

## 功能特性

- ✅ 自动适配不同设备的状态栏高度
- ✅ 自动获取并适配小程序胶囊按钮位置
- ✅ 支持自定义标题、颜色、背景色
- ✅ 支持显示/隐藏返回按钮
- ✅ 支持自定义返回按钮文字
- ✅ 右侧自动为小程序胶囊按钮预留空间
- ✅ 支持固定定位和相对定位

## 使用方法

### 1. 页面配置

首先确保你的页面在 `pages.json` 中设置了自定义导航栏：

```json
{
  "path": "myPackge5/pages/example",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "示例页面"
  }
}
```

### 2. 引入组件

在页面中引入并注册组件：

```vue
<script>
import CustomNavbar from './components/custom-navbar.vue'

export default {
  components: {
    CustomNavbar
  }
}
</script>
```

### 3. 使用组件

```vue
<template>
  <view class="page">
    <!-- 基础使用 -->
    <custom-navbar title="页面标题" />
    
    <!-- 完整配置 -->
    <custom-navbar 
      :title="pageTitle" 
      :show-back="true"
      :back-text="返回文字"
      :bg-color="'#007AFF'"
      :title-color="'#ffffff'"
      :back-color="'#ffffff'"
      :fixed="true"
      @back="handleBack"
    />
    
    <!-- 页面内容需要预留导航栏空间 -->
    <view class="content" :style="contentStyle">
      <!-- 页面内容 -->
    </view>
  </view>
</template>

<script>
import { getSystemInfo } from '@/utils/getLocation'

export default {
  computed: {
    contentStyle() {
      // 为固定定位的导航栏预留空间
      const systemInfo = getSystemInfo()
      return {
        paddingTop: systemInfo.customBar + 'px'
      }
    }
  },
  methods: {
    handleBack() {
      // 自定义返回逻辑
      console.log('返回按钮被点击')
    }
  }
}
</script>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | '' | 导航栏标题 |
| showBack | Boolean | true | 是否显示返回按钮 |
| backText | String | '' | 返回按钮文字（可选） |
| bgColor | String | '#ffffff' | 导航栏背景颜色 |
| titleColor | String | '#333333' | 标题文字颜色 |
| backColor | String | '#333333' | 返回按钮颜色 |
| fixed | Boolean | true | 是否固定定位 |

## 组件事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| back | 返回按钮点击事件 | - |

## 样式说明

组件会自动处理以下样式：

1. **状态栏适配**：自动获取设备状态栏高度并添加占位
2. **胶囊按钮适配**：自动获取胶囊按钮尺寸和位置，为其预留空间
3. **导航栏高度**：根据胶囊按钮高度自动计算导航栏高度
4. **左右边距**：左侧预留返回按钮空间，右侧预留胶囊按钮空间

## 注意事项

1. 使用该组件的页面必须设置 `"navigationStyle": "custom"`
2. 页面内容需要为固定定位的导航栏预留空间（使用 `padding-top`）
3. 组件依赖 `@/utils/getLocation` 中的 `getSystemInfo` 函数
4. 在不支持 `uni.getMenuButtonBoundingClientRect()` 的环境中会使用默认值

## 示例页面

查看 `navbar-demo.vue` 文件可以看到完整的使用示例和配置演示。

## 兼容性

- ✅ 微信小程序
- ✅ 支付宝小程序
- ✅ 百度小程序
- ✅ 字节跳动小程序
- ✅ QQ小程序
