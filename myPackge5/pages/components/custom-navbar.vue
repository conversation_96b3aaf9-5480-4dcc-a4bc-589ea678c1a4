<template>
  <view class="custom-navbar" :style="navbarStyle">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="statusBarStyle"></view>
    
    <!-- 导航栏内容 -->
    <view class="navbar-content" :style="contentStyle">
      <!-- 左侧返回按钮区域 -->
      <view class="nav-left" @click="handleBack" v-if="showBack">
        <view class="back-btn">
          <text class="back-icon">‹</text>
          <text class="back-text" v-if="backText">{{ backText }}</text>
        </view>
      </view>
      
      <!-- 标题区域 -->
      <view class="nav-title" :style="titleStyle">
        <text class="title-text">{{ title }}</text>
      </view>
      
      <!-- 右侧胶囊按钮占位区域 -->
      <view class="nav-right" :style="capsuleStyle"></view>
    </view>
  </view>
</template>

<script>
import { getSystemInfo } from '@/utils/getLocation'

export default {
  name: 'CustomNavbar',
  props: {
    // 导航栏标题
    title: {
      type: String,
      default: ''
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      default: true
    },
    // 返回按钮文字
    backText: {
      type: String,
      default: ''
    },
    // 导航栏背景颜色
    bgColor: {
      type: String,
      default: '#ffffff'
    },
    // 标题颜色
    titleColor: {
      type: String,
      default: '#333333'
    },
    // 返回按钮颜色
    backColor: {
      type: String,
      default: '#333333'
    },
    // 是否固定定位
    fixed: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      systemInfo: {},
      statusBarHeight: 0,
      navbarHeight: 44,
      capsuleInfo: {}
    }
  },
  computed: {
    // 导航栏整体样式
    navbarStyle() {
      return {
        position: this.fixed ? 'fixed' : 'relative',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 999,
        backgroundColor: this.bgColor
      }
    },
    // 状态栏样式
    statusBarStyle() {
      return {
        height: this.statusBarHeight + 'px'
      }
    },
    // 导航栏内容样式
    contentStyle() {
      return {
        height: this.navbarHeight + 'px',
        paddingLeft: '16px',
        paddingRight: (this.capsuleInfo.width + 16) + 'px'
      }
    },
    // 标题样式
    titleStyle() {
      return {
        color: this.titleColor
      }
    },
    // 胶囊按钮占位样式
    capsuleStyle() {
      return {
        width: this.capsuleInfo.width + 'px',
        height: this.capsuleInfo.height + 'px'
      }
    }
  },
  created() {
    this.initNavbar()
  },
  methods: {
    // 初始化导航栏
    initNavbar() {
      // 获取系统信息
      this.systemInfo = getSystemInfo()
      this.statusBarHeight = this.systemInfo.statusBar
      
      // 获取胶囊按钮信息
      try {
        this.capsuleInfo = uni.getMenuButtonBoundingClientRect()
        // 计算导航栏高度：胶囊按钮高度 + 上下边距
        const capsuleTop = this.capsuleInfo.top - this.statusBarHeight
        this.navbarHeight = this.capsuleInfo.height + capsuleTop * 2
      } catch (e) {
        // 如果获取胶囊信息失败，使用默认值
        this.capsuleInfo = {
          width: 87,
          height: 32
        }
        this.navbarHeight = 44
      }
    },
    
    // 返回按钮点击事件
    handleBack() {
      this.$emit('back')
      // 默认返回上一页
      if (getCurrentPages().length > 1) {
        uni.navigateBack({
          delta: 1
        })
      } else {
        // 如果是首页，可以跳转到指定页面或者不做处理
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-navbar {
  width: 100%;
  background-color: #ffffff;
}

.status-bar {
  width: 100%;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
}

.nav-left {
  display: flex;
  align-items: center;
  height: 100%;
  
  .back-btn {
    display: flex;
    align-items: center;
    padding: 8px 0;
    
    .back-icon {
      font-size: 24px;
      font-weight: bold;
      margin-right: 4px;
    }
    
    .back-text {
      font-size: 16px;
    }
  }
}

.nav-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 16px;
  
  .title-text {
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
  }
}

.nav-right {
  flex-shrink: 0;
}
</style>
