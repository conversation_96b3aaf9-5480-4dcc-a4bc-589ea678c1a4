<!-- 首页 -->
<template>
    <view class="page-container">
        <custom-navbar title="活畜管理" />
    </view>
</template>

<script>
import CustomNavbar from './components/custom-navbar.vue'

export default {
    components: {
        CustomNavbar
    },
    data() {
        return {

        }
    },
    async onLoad(options) {

    },
    onShow() {
    },
    onReady() {

    },
    methods: {}
}
</script>
<style scoped lang="scss">
.page-container {
    background-color: #fff !important;
}
</style>